import { Button, Drawer, Form, Input, InputNumber, message, Select, Space, Switch, Typography } from 'antd'
import React, { useEffect } from 'react'
import {
  BUDGET_CATEGORY_OPTIONS,
  BudgetCategory,
  getBudgetSubcategoryLabel,
  getSubcategoryOptionsByCategory,
} from '../../../../../consts/budget'
import { IProductionListItem } from '../../list/store'
import useProjectDetailStore, { IPrProductionBudgetItem } from '../store'

const { TextArea } = Input
const { Text } = Typography

interface IEditBudgetItemProps {
  open: boolean
  onCancel: () => void
  onSuccess: () => void
  loading?: boolean
  productionId: number
  project?: IProductionListItem
  editingItem?: IPrProductionBudgetItem // 编辑的预算项
}

const EditBudgetItem: React.FC<IEditBudgetItemProps> = ({
  open,
  onCancel,
  onSuccess,
  loading = false,
  productionId,
  project,
  editingItem,
}) => {
  const [form] = Form.useForm()
  const { saveBudgetItems } = useProjectDetailStore()

  // 判断是否为编辑模式
  const isEdit = !!editingItem?.id

  // 监听一级分类变化，清空二级分类
  const handleCategoryChange = () => {
    form.setFieldValue('subcategory', undefined)
  }

  // 获取当前选中一级分类对应的二级分类选项
  const selectedCategory = Form.useWatch('category', form)
  const subcategoryOptions = selectedCategory ? getSubcategoryOptionsByCategory(selectedCategory) : []

  useEffect(() => {
    if (open && editingItem) {
      // 编辑模式：填充现有数据
      const firstDetail = editingItem.itemsDetail?.[0]
      form.setFieldsValue({
        itemName: editingItem.itemName || '',
        quotedPrice: editingItem.quotedPrice,
        personCount: editingItem.personCount || 1,
        dayCount: editingItem.dayCount || 1,
        totalPrice: editingItem.totalPrice,
        hasInvoice: editingItem.hasInvoice === 1,
        description: editingItem.description || '',
        // 从第一个itemsDetail获取分类信息
        category: firstDetail?.category,
        subcategory: firstDetail?.subcategory,
      })
    } else if (open) {
      // 新增模式：重置表单
      form.resetFields()
      form.setFieldsValue({
        personCount: 1,
        dayCount: 1,
        hasInvoice: false,
      })
    }
  }, [open, editingItem, form])

  // 处理保存
  const handleSave = async (values: any) => {
    try {
      const budgetItem: IPrProductionBudgetItem = {
        id: editingItem?.id,
        productionId,
        isPackage: editingItem?.isPackage || false,
        itemName: values.itemName,
        quotedPrice: values.quotedPrice,
        personCount: values.personCount,
        dayCount: values.dayCount,
        totalPrice: values.totalPrice,
        hasInvoice: values.hasInvoice ? 1 : 0,
        description: values.description,
        itemsDetail: [
          {
            itemId: editingItem?.id || 0,
            category: values.category,
            subcategory: values.subcategory,
            personCount: values.personCount,
          },
        ],
      }

      const success = await saveBudgetItems({
        productionId,
        budgetItems: [budgetItem],
      })

      if (success) {
        message.success(isEdit ? '编辑预算项成功' : '添加预算项成功')
        onSuccess()
        onCancel()
      } else {
        message.error(isEdit ? '编辑预算项失败' : '添加预算项失败')
      }
    } catch (error) {
      console.error('保存预算项失败:', error)
      message.error(isEdit ? '编辑预算项失败' : '添加预算项失败')
    }
  }

  return (
    <Drawer
      title={isEdit ? '编辑预算项' : '添加预算项'}
      open={open}
      onClose={onCancel}
      width={600}
      extra={
        <Space>
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" loading={loading} onClick={() => form.submit()}>
            {isEdit ? '保存' : '添加'}
          </Button>
        </Space>
      }>
      <Form form={form} layout="vertical" onFinish={handleSave}>
        {editingItem?.isPackage && (
          <Form.Item
            label="打包名称"
            name="itemName">
            <Input placeholder="请输入打包名称" />
          </Form.Item>
        )}

        <Form.Item
          label="一级分类"
          name="category"
          rules={[{ required: true, message: '请选择一级分类' }]}
          hidden
          >
          <Select
            placeholder="请选择一级分类"
            options={BUDGET_CATEGORY_OPTIONS}
            onChange={handleCategoryChange}
          />
        </Form.Item>

        <Form.Item
          label="二级分类"
          name="subcategory"
          rules={[{ required: true, message: '请选择二级分类' }]}
          >
          <Select
            placeholder="请选择二级分类"
            options={subcategoryOptions}
            disabled={true}
          />
        </Form.Item>

        <Space direction="vertical" style={{ width: '100%' }}>
          <Form.Item
            label={`单价 (${project?.currencySymbol || '¥'})`}
            name="quotedPrice"
            rules={[{ required: true, message: '请输入单价' }]}>
            <InputNumber
              placeholder="请输入单价"
              min={0}
              precision={2}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Space style={{ width: '100%' }}>
            <Form.Item
              label="人数"
              name="personCount"
              rules={[{ required: true, message: '请输入人数' }]}>
              <InputNumber
                placeholder="请输入人数"
                min={1}
                style={{ width: 120 }}
              />
            </Form.Item>

            <Form.Item
              label="天数"
              name="dayCount"
              rules={[{ required: true, message: '请输入天数' }]}>
              <InputNumber
                placeholder="请输入天数"
                min={1}
                style={{ width: 120 }}
              />
            </Form.Item>
          </Space>

          <Form.Item
            label={`总价 (${project?.currencySymbol || '¥'})`}
            name="totalPrice"
            rules={[{ required: true, message: '请输入总价' }]}>
            <InputNumber
              placeholder="请输入总价"
              min={0}
              precision={2}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item label="是否有发票" name="hasInvoice" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item label="备注说明" name="description">
            <TextArea
              placeholder="请输入备注说明"
              rows={3}
              maxLength={500}
              showCount
            />
          </Form.Item>
        </Space>
      </Form>
    </Drawer>
  )
}

export default EditBudgetItem
