import { DeleteOutlined, DownloadOutlined, EditOutlined, FolderAddOutlined, PlusOutlined } from '@ant-design/icons'
import { Fieldset, ListHeader } from '@fe/rockrose'
import { Button, Card, Descriptions, Empty, Flex, message, Popconfirm, Space, Table, Typography } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import {
  BUDGET_CATEGORY_CONFIG,
  BudgetCategory,
  getBudgetCategoryLabel,
  getBudgetSubcategoryLabel,
} from '../../../../../consts/budget'
import { IProductionListItem } from '../../list/store'
import useProjectDetailStore, { IPrProductionBudgetItem, IPrProductionItemsDetail } from '../store'
import { exportBudgetById } from '../../../../../utils/export'
import AddNewBudgetExpenses from './AddNewBudgetExpenses'
import AddNewBudgetPackage from './AddNewBudgetPackage'
import EditBudgetItem from './EditBudgetItem'

const { Text } = Typography

interface INewBudgetTabProps {
  productionId: number
  project?: IProductionListItem
}

// 展示用的预算项目数据结构
interface IBudgetDisplayItem extends IPrProductionItemsDetail {
  budgetItemIndex: number // 对应的BudgetItem索引
  budgetItemName?: string // 打包名称（如果是打包项）
  isPackage: boolean // 是否为打包项
  quotedPrice?: number // 单价
  dayCount: number // 拍摄天数
  totalPrice?: number // 总金额
  hasInvoice?: number // 是否正规发票
  description?: string // 备注说明
}

// 二级渲染数据结构（参考excel）
interface IBudgetGroupData {
  category: BudgetCategory
  categoryLabel: string
  items: IBudgetDisplayItem[]
  totalPrice: number
}

const NewBudgetTab: React.FC<INewBudgetTabProps> = ({ productionId, project }) => {
  const [budgetItems, setBudgetItems] = useState<IPrProductionBudgetItem[]>([])
  const [loading, setLoading] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)
  const [isPackageModalOpen, setIsPackageModalOpen] = useState(false)
  const [isExpensesModalOpen, setIsExpensesModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingItem, setEditingItem] = useState<IPrProductionBudgetItem | undefined>()

  const { getBudgetItems, deleteBudgetItems } = useProjectDetailStore()

  useEffect(() => {
    if (productionId) {
      loadBudgetItems()
    }
  }, [productionId, refreshKey])

  // 加载预算项目列表
  const loadBudgetItems = async () => {
    setLoading(true)
    try {
      const result = await getBudgetItems({ productionId })
      setBudgetItems(result || [])
    } catch (error) {
      message.error('加载预算项目失败')
    } finally {
      setLoading(false)
    }
  }

  // 使用useMemo处理数据转换（参考BudgetOverviewTab和excel csv排序）
  const { groupedBudgetData, statistics } = useMemo(() => {
    // 1. 将itemsDetail取出变为一份新数组，保留BudgetItem的索引
    const displayItems: IBudgetDisplayItem[] = []
    budgetItems.forEach((budgetItem, budgetItemIndex) => {
      if (budgetItem.itemsDetail && budgetItem.itemsDetail.length > 0) {
        budgetItem.itemsDetail.forEach(detail => {
          displayItems.push({
            ...detail,
            budgetItemIndex,
            budgetItemName: budgetItem.isPackage ? budgetItem.itemName : undefined,
            isPackage: budgetItem.isPackage || false,
            quotedPrice: budgetItem.quotedPrice,
            dayCount: budgetItem.dayCount,
            totalPrice: budgetItem.totalPrice,
            hasInvoice: budgetItem.hasInvoice,
            description: budgetItem.description,
          })
        })
      }
    })

    // 2. 按分类分组（参考excel csv的顺序）
    const categoryOrder = [
      { category: BudgetCategory.VENUE_RENTAL, label: '场地费用', key: 'venue' },
      { category: BudgetCategory.PRODUCTION, label: '制片', key: 'production' },
      { category: BudgetCategory.DIRECTOR, label: '导演', key: 'director' },
      { category: BudgetCategory.PHOTOGRAPHY, label: '摄影', key: 'photography' },
      { category: BudgetCategory.PHOTOGRAPHY_EQUIPMENT, label: '摄影器材', key: 'equipment' },
      { category: BudgetCategory.SOUND, label: '收音', key: 'sound' },
      { category: BudgetCategory.POST_PRODUCTION, label: '后期', key: 'postproduction' },
      { category: BudgetCategory.LIGHTING, label: '灯光', key: 'lighting' },
      { category: BudgetCategory.ART, label: '美术组', key: 'art' },
      { category: BudgetCategory.COSTUME_MAKEUP, label: '服化组', key: 'costume' },
      { category: BudgetCategory.ACTOR, label: '演员', key: 'actor' },
      { category: BudgetCategory.MISCELLANEOUS, label: '制片费', key: 'miscellaneous' },
    ]

    // 3. 生成分组数据和统计（修复打包项重复计算问题）
    const groupedBudgetData: Array<{
      category: BudgetCategory
      label: string
      key: string
      items: IBudgetDisplayItem[]
      subtotal: number
    }> = []

    let totalBudget = 0
    let venueTotal = 0
    let personTotal = 0
    let expenseTotal = 0

    // 用于记录已计算过的打包项，避免重复计算
    const calculatedPackageItems = new Set<number>()

    categoryOrder.forEach(({ category, label, key }) => {
      const items = displayItems.filter(item => item.category === category)
      if (items.length === 0) return

      // 修复打包项重复计算问题：打包项只计算一次总价
      let subtotal = 0
      items.forEach(item => {
        if (item.isPackage) {
          // 打包项：只在第一次遇到时计算总价
          if (!calculatedPackageItems.has(item.budgetItemIndex)) {
            subtotal += item.totalPrice || 0
            calculatedPackageItems.add(item.budgetItemIndex)
          }
        } else {
          // 非打包项：每个都计算
          subtotal += item.totalPrice || 0
        }
      })

      totalBudget += subtotal

      // 分类统计
      if (category === BudgetCategory.VENUE_RENTAL) {
        venueTotal += subtotal
      } else if (category === BudgetCategory.MISCELLANEOUS) {
        expenseTotal += subtotal
      } else {
        personTotal += subtotal
      }

      groupedBudgetData.push({
        category,
        label,
        key,
        items,
        subtotal,
      })
    })

    // 4. 计算统计数据
    const statistics = {
      venueTotal,
      personTotal,
      expenseTotal,
      totalBudget,
      taxTotal: totalBudget * 1.03, // 含税3%
    }

    return { groupedBudgetData, statistics }
  }, [budgetItems])

  // 处理添加打包项
  const handleAddPackage = () => {
    setIsPackageModalOpen(true)
  }

  // 处理添加费用项
  const handleAddExpense = () => {
    setIsExpensesModalOpen(true)
  }

  // 处理添加成功后的刷新
  const handleAddSuccess = () => {
    setRefreshKey(prev => prev + 1)
    setEditingItem(undefined)
  }

  // 处理编辑预算项
  const handleEdit = (record: IBudgetDisplayItem) => {
    // 根据budgetItemIndex找到对应的BudgetItem
    const budgetItem = budgetItems[record.budgetItemIndex]
    if (budgetItem) {
      setEditingItem(budgetItem)
      setIsEditModalOpen(true)
    }
  }

  // 处理删除预算项
  const handleDelete = async (record: IBudgetDisplayItem) => {
    const budgetItem = budgetItems[record.budgetItemIndex]
    if (!budgetItem?.id) {
      message.error('无法删除该项目')
      return
    }

    try {
      const success = await deleteBudgetItems(budgetItem.id)
      if (success) {
        message.success('删除成功')
        setRefreshKey(prev => prev + 1)
      } else {
        message.error('删除失败')
      }
    } catch (error) {
      console.error('删除预算项失败:', error)
      message.error('删除失败')
    }
  }

  // 处理导出预算表
  const handleExport = async () => {
    if (!productionId) {
      message.error('项目ID不存在，无法导出')
      return
    }

    try {
      await exportBudgetById(productionId, `预算表_${project?.productionName || '项目'}_${new Date().toLocaleDateString()}`)
      message.success('导出成功')
    } catch (error) {
      console.error('导出预算表失败:', error)
      message.error('导出失败')
    }
  }

  // 定义表格列（参考BudgetOverviewTab）
  const getColumns = () => [
    {
      title: '项目',
      dataIndex: 'subcategory',
      key: 'subcategory',
      width: '25%',
      align: 'center' as const,
      render: (subcategory: number, record: IBudgetDisplayItem) => (
        <Space direction="vertical" size={4}>
          <Text strong>{record.subcategoryLabel || getBudgetSubcategoryLabel(subcategory)}</Text>
          {record.isPackage && record.budgetItemName && (
            <Text type="secondary">({record.budgetItemName})</Text>
          )}
        </Space>
      ),
    },
    {
      title: '数量',
      dataIndex: 'personCount',
      key: 'personCount',
      width: 80,
      align: 'center' as const,
      render: (count: number) => count || 1,
    },
    {
      title: '拍摄天数',
      dataIndex: 'dayCount',
      key: 'dayCount',
      width: 100,
      align: 'center' as const,
      render: (count: number) => count || 1,
    },
    {
      title: '单价',
      dataIndex: 'quotedPrice',
      key: 'quotedPrice',
      width: 120,
      align: 'center' as const,
      render: (price: number) => price ? `${project?.currencySymbol || '¥'}${price.toLocaleString()}` : '-',
    },
    {
      title: '总金额',
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      width: 120,
      align: 'center' as const,
      render: (price: number, record: IBudgetDisplayItem) => (
        <Space direction="vertical" size={4}>
          <Text type="danger" strong>
            {price ? `${project?.currencySymbol || '¥'}${price.toLocaleString()}` : '-'}
          </Text>
          {record.isPackage && record.budgetItemName && (
            <Text type="secondary">({record.budgetItemName})</Text>
          )}
        </Space>
      ),
    },
    {
      title: '有无发票',
      dataIndex: 'hasInvoice',
      key: 'hasInvoice',
      width: 100,
      align: 'center' as const,
      render: (hasInvoice: number) => (
        hasInvoice ? <Text type="success">是</Text> : <Text type="secondary">否</Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 140,
      align: 'center' as const,
      render: (_: any, record: IBudgetDisplayItem) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Popconfirm
            title="确认删除"
            description="删除后无法恢复，确认删除该预算项吗？"
            onConfirm={() => handleDelete(record)}
            okText="确认"
            cancelText="取消">
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  // 渲染分组表格数据（参考BudgetOverviewTab）
  const renderGroupedTables = () => {
    if (groupedBudgetData.length === 0) {
      return <Empty description="暂无预算数据" />
    }

    return (
      <Space direction="vertical" size={24} style={{ width: '100%' }}>
        {groupedBudgetData.map(group => (
          <Fieldset
            key={group.key}
            title={
              <Flex justify="space-between" align="center">
                <Text strong>{group.label}小计: </Text>
                <Text type="danger" strong>
                  {`${project?.currencySymbol || '¥'}${group.subtotal.toLocaleString()}`}
                </Text>
              </Flex>
            }>
            <Table
              columns={getColumns()}
              dataSource={group.items}
              rowKey={(record, index) => `${group.key}-${record.budgetItemIndex}-${record.id || record.subcategory}-${index}`}
              pagination={false}
              size="small"
              locale={{ emptyText: `暂无${group.label}数据` }}
              style={{ width: '100%' }}
              tableLayout="fixed"
              scroll={{ x: true }}
            />
          </Fieldset>
        ))}

        {/* 总计统计 */}
        <Card>
          <Descriptions
            title="预算统计"
            bordered
            column={2}
            size="small">
            <Descriptions.Item label="场地费用总计">
              <Text type="danger" strong>
                {`${project?.currencySymbol || '¥'}${statistics.venueTotal.toLocaleString()}`}
              </Text>
            </Descriptions.Item>
            <Descriptions.Item label="人工费用总计">
              <Text type="danger" strong>
                {`${project?.currencySymbol || '¥'}${statistics.personTotal.toLocaleString()}`}
              </Text>
            </Descriptions.Item>
            {/* <Descriptions.Item label="制片费总计">
              <Text type="danger" strong>
                {`${project?.currencySymbol || '¥'}${statistics.expenseTotal.toLocaleString()}`}
              </Text>
            </Descriptions.Item> */}
            <Descriptions.Item label="预算总金额总计（不含税）">
              <Text type="danger" strong className="fs-lg">
                {`${project?.currencySymbol || '¥'}${statistics.totalBudget.toLocaleString()}`}
              </Text>
            </Descriptions.Item>
            <Descriptions.Item label="预算总金额总计（含税点3%）" span={2}>
              <Text type="danger" strong className="fs-lg">
                {`${project?.currencySymbol || '¥'}${statistics.taxTotal.toLocaleString()}`}
              </Text>
            </Descriptions.Item>
          </Descriptions>
        </Card>
      </Space>
    )
  }

  return (
    <Flex vertical>
      <ListHeader
        title={
          <Space>
            <span>新预算总计</span>
            <Text type="danger" strong className="fs-lg">
              {`${project?.currencySymbol || '¥'}`}
              {statistics.totalBudget.toLocaleString()}
            </Text>
          </Space>
        }>
        <Space>
          <Button
            color="primary"
            variant="filled"
            shape="round"
            icon={<FolderAddOutlined />}
            onClick={handleAddPackage}>
            添加打包
          </Button>
          <Button
            type="primary"
            ghost
            shape="round"
            icon={<PlusOutlined />}
            onClick={handleAddExpense}>
            添加费用
          </Button>
          <Button
            shape="round"
            icon={<DownloadOutlined />}
            onClick={handleExport}>
            导出预算表
          </Button>
        </Space>
      </ListHeader>

      <div style={{ height: 'calc(100% - 60px)', overflow: 'auto',paddingTop:'30px' }}>
        {loading ? (
          <Table loading={true} columns={[]} dataSource={[]} />
        ) : (
          renderGroupedTables()
        )}
      </div>

      {/* 添加打包项模态框 */}
      {isPackageModalOpen && (
        <AddNewBudgetPackage
          open={isPackageModalOpen}
          onCancel={() => {
            setIsPackageModalOpen(false)
            setEditingItem(undefined)
          }}
          onSuccess={handleAddSuccess}
          productionId={productionId}
          project={project}
          editingItem={editingItem}
        />
      )}

      {/* 添加费用项模态框 */}
      {isExpensesModalOpen && (
        <AddNewBudgetExpenses
          open={isExpensesModalOpen}
          onCancel={() => {
            setIsExpensesModalOpen(false)
            setEditingItem(undefined)
          }}
          onSuccess={handleAddSuccess}
          productionId={productionId}
          project={project}
        />
      )}

      {/* 编辑预算项模态框 */}
      {isEditModalOpen && (
        <EditBudgetItem
          open={isEditModalOpen}
          onCancel={() => {
            setIsEditModalOpen(false)
            setEditingItem(undefined)
          }}
          onSuccess={handleAddSuccess}
          productionId={productionId}
          project={project}
          editingItem={editingItem}
        />
      )}
    </Flex>
  )
}

export default NewBudgetTab
